<!-- // 所有考生 -->
<template>
  <div class="all">
    
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="姓名">
        <el-input v-model="condition.name" placeholder="姓名"></el-input>
      </el-form-item>
      <!-- <el-form-item label="应聘职位">
        <el-input v-model="condition.institute" placeholder="应聘职位"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="专业">
        <el-input v-model="condition.major" placeholder="专业"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="年级">
        <el-input v-model="condition.grade" placeholder="年级"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="子部门">
        <el-input v-model="condition.clazz" placeholder="子部门"></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="联系方式">
        <el-input v-model="condition.tel" placeholder="联系方式"></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="getStudentGrade"  style="font-size: 20px;"  size="mini"> 查询</el-button>
        <el-button type="primary" @click="exportGrad"  style="font-size: 20px;"  size="mini"> 导出</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="pagination.records" border>
      <el-table-column fixed="left" prop="studentName" label="姓名" ></el-table-column>
      <!-- <el-table-column prop="institute" label="应聘职位" ></el-table-column> -->
      <!-- <el-table-column prop="major" label="专业"></el-table-column> -->
      <!-- <el-table-column prop="grade" label="年级" width="200"></el-table-column> -->
      <!-- <el-table-column prop="clazz" label="子部门" ></el-table-column> -->
      <el-table-column prop="sex" label="性别" ></el-table-column>
      <el-table-column prop="tel" label="联系方式" ></el-table-column>
      <el-table-column fixed="right" label="查看成绩" >
        <template slot-scope="scope">
          <el-button @click="checkGrade(scope.row.studentId)" type="primary" size="mini"> 查看成绩</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="grade" label="成绩" ></el-table-column> -->
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.current"
      :page-sizes="[6, 10]"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      class="page"
    ></el-pagination>
  </div>
</template>

<script>
  import { getQj , postQj, deleteQj , putQj} from "@/api/index";
  import {exportExcel} from "@/utils/zipdownload";
export default {
  data() {
    return {
      pagination: {
        //分页后的考试信息
        current: 1, //当前页
        total: null, //记录条数
        size: 6 //每页条数
      },
      condition: {
        name: "",
        tel: "",
        grade: "",
        clazz: "",
        major: "",
        institute: "",
      }
    };
  },
  created() {
    this.getStudentGrade();
  },
  methods: {
    exportGrad(){
      console.log("导出")
      // const queryParams = this.queryParams;
      this.$confirm('是否确认导出成绩单?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          closeOnClickModal:false,
          type: "warning"
        }).then(() => {
          let fileName = '成绩单' + Date.now();
          // 开始导出
          exportExcel("/export/score-list", {},fileName);
        }).catch(() => {});
    },
    getStudentGrade() {
      // 根据条件获取考生成绩
      let name =this.condition.name.trim();
      let grade = this.condition.grade.trim();
      let tel = this.condition.tel.trim();
      let institute = this.condition.institute.trim();
      let major = this.condition.major.trim();
      let clazz = this.condition.clazz.trim();
      getQj(`/students/findAll?page=${this.pagination.current}&size=${this.pagination.size}&name=${name}&grade=${grade}&tel=${tel}&institute=${institute}&major=${major}&clazz=${clazz}`).then(res => {
        this.pagination = res.data;
      }).catch(error => {});
    },
    //改变当前记录条数
    handleSizeChange(val) {
      this.pagination.size = val;
      this.getStudentGrade();
    },
    //改变当前页码，重新发送请求
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.getStudentGrade();
    },
    checkGrade(studentId) {
      this.$router.push({ path: "/grade", query: { studentId: studentId } });
    }
  }
};
</script>
<style lang="less" scoped>
.all {
  padding: 0px 40px;
  .page {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edit {
    margin-left: 20px;
  }
  .el-table tr {
    background-color: #dd5862 !important;
  }
}
.el-table .warning-row {
  background: #000 !important;
}

.el-table .success-row {
  background: #dd5862;
}
</style>
