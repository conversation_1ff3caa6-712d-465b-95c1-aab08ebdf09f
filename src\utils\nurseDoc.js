// 自定义护理文书，获取表单元素内容
export function getFormData() {
  // 获取表单元素的引用
  let form = document.getElementById("myForm");
  // console.log(form);
  // 创建一个空对象，用于存储表单数据
  let formData = {};
  // 遍历表单中的所有输入字段
  for (let i = 0; i < form.elements.length; i++) {
    let element = form.elements[i];
    // 忽略不是输入字段的元素（如按钮）
    if (element.type !== "button") {
      // 忽略不是输入字段的元素（如按钮）
      if (element.type !== "button") {
        // 对于单选框，如果未选中，设置为空字符串
        if (element.type === "radio") {
          if (element.checked) {
            // 使用输入字段的名称作为键，值作为值，添加到formData对象中
            formData[element.name] = element.value;
          } else if (!formData.hasOwnProperty(element.name)) {
            // 如果还没有任何单选框选中，设置为空字符串
            formData[element.name] = "";
          }
        } else if (element.type === "checkbox") {
          if (!formData[element.name]) {
            formData[element.name] = [];
          }
          // 获取所有的复选框元素
          let obj1 = element.checked;
          if (obj1) {
            formData[element.name].push(element.value);
          }
        } else {
          // 使用输入字段的名称作为键，值作为值，添加到formData对象中
          formData[element.name] = element.value;
          // if (element.name == 'noticeTime') {
          //     // console.log(element.dataset, 'element.dataset==========')
          //     // console.log(element.dataset.dt, 'element.dataset.dt==========')
          // }
        }
      }
    }
  }
  return formData;
}

export function setFormData() {
  // 获取表单元素的引用
  let form = document.getElementById("myForm");
  // 创建一个空对象，用于存储表单数据
  let formData = {};
  // 遍历表单中的所有输入字段
  for (let i = 0; i < form.elements.length; i++) {
    let element = form.elements[i];
    // 忽略不是输入字段的元素（如按钮）
    if (element.type !== "button") {
      // 忽略不是输入字段的元素（如按钮）
      if (element.type !== "button") {
        // 对于单选框，如果未选中，设置为空字符串
        if (element.type === "radio") {
          if (element.checked) {
            // 使用输入字段的名称作为键，值作为值，添加到formData对象中
            formData[element.name] = element.value;

            let regex = /type="radio"/g; // 使用全局正则表达式
            let str = element.outerHTML;
            let newStr = str.replace(regex, 'type="radio" checked');
            element.outerHTML = newStr;
          } else if (!formData.hasOwnProperty(element.name)) {
            // 如果还没有任何单选框选中，设置为空字符串
            formData[element.name] = "";
          }
        } else if (element.type === "checkbox") {
          if (element.checked) {
            let regex = /type="checkbox"/g; // 使用全局正则表达式
            let str = element.outerHTML;
            let newStr = str.replace(regex, 'type="checkbox" checked');
            element.outerHTML = newStr;
          }
        } else if (element.type === "text") {
          // 使用输入字段的名称作为键，值作为值，添加到formData对象中
          if (element.value) {
            formData[element.name] = element.value;
            let regex = /type="text"/g; // 使用全局正则表达式
            let str = element.outerHTML;
            let newStr = str.replace(
              regex,
              `type="text" value=${element.value}`
            );
            element.outerHTML = newStr;
          }
        } else {
          formData[element.name] = element.value;
        }
      }
    }
  }
  return form;
}

export function getInnerhtml() {
  const element = document.getElementById("dochtml");
  return element.innerHTML;
}
