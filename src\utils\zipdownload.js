import axios from 'axios'
// import {getToken} from '@/utils/auth'
import { baseURL } from './configUrl'
import { Loading } from "element-ui";
import { Notification, MessageBox, Message } from "element-ui";
import VueCookies from 'vue-cookies'
import store from '@/vuex/store'
const mimeMap = {
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  zip: 'application/zip'
}
const baseUrl = baseURL


let rb_token = VueCookies.get("cardId") || sessionStorage.getItem("cname");
let rb_role = VueCookies.get("role")  || sessionStorage.getItem("role");
let cname = VueCookies.get("cname")  || sessionStorage.getItem("cname");
let cid = VueCookies.get("cid")  || sessionStorage.getItem("cid");

let userInfo = store.state.userInfo;
let Authorization = '';
if(store.state.userInfo){
  Authorization = `cardId=${userInfo.cardId};rb_token=${userInfo.cardId};rb_role=${userInfo.role};cid=${userInfo.adminId};role=${userInfo.role}`; // 让每个请求携带自定义token 请根据实际情况自行修改
}else if(rb_token) {
  Authorization = `cardId=${rb_token};rb_token=${rb_token};rb_role=${rb_role};cid=${cid};role=${rb_role}`; // 让每个请求携带自定义token 请根据实际情况自行修改
}


export function downLoadZip(str, filename) {
  var url = baseUrl + str
  axios({
    method: 'get',
    url: url,
    responseType: 'blob',
    headers: {'Authorization': Authorization}
  }).then(res => {
    resolveBlob(res, mimeMap.zip)
  })
}

export function exportExcel(url, params,fileName) {
  let loadingInstance = Loading.service({
      text: '请稍等...',
  });
  
  url = baseUrl + splicingParam(url, params);
  console.log(url, params,fileName)
  axios({
    method: 'get',
    url: url,
    responseType: 'blob',
    headers:{'Authorization': Authorization},
  }).then(res => {
    console.log(res)
    loadingInstance.close();
    // 判断Bolb类型是否有错误信息
    if ( res.data instanceof Blob && res.data.type === 'application/json' ) {
      let reader = new FileReader()
        reader.readAsText(res.data, 'utf-8')
        reader.onload = function(e) {
          let errorArr = [];
          if(e.target.result){
            errorArr = JSON.parse(e.target.result);
          }
          Message.error(errorArr.msg)
        }
    }else{
      resolveBlob(res, mimeMap.xlsx,fileName)
    }
  }).catch(error=>{
    console.log(error)
  })
}

export function splicingParam(url, params) {
  let str = url + '?';
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && typeof (value) !== "undefined") {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          let params = propName + '[' + key + ']';
          var subPart = encodeURIComponent(params) + "=";
          str += subPart + encodeURIComponent(value[key]) + "&";
        }
      } else {
        str += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return str.slice(0, -1);
}

/**
 * 解析blob响应内容并下载
 * @param {*} res blob响应内容
 * @param {String} mimeType MIME类型
 */
export function resolveBlob(res, mimeType,crfileName) {
  console.log(res)
  const aLink = document.createElement('a')
  var blob = new Blob([res.data], {type: mimeType})
  // //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
  var patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*');
  var contentDisposition = decodeURI(res.headers['content-disposition']);

  console.log(contentDisposition);

  let fileName = '';
  if(contentDisposition != "undefined"){
    var result = patt.exec(contentDisposition)
    fileName = result ? result[1] : '附件.xls';
    fileName = fileName.replace(/\"/g, '')
  }else{
    fileName = crfileName + '.xls'
  }

  aLink.href = URL.createObjectURL(blob)
  aLink.setAttribute('download', fileName) // 设置下载文件名称
  document.body.appendChild(aLink)
  aLink.click()
  document.body.removeChild(aLink);
}
