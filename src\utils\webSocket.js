let webSocket = null;
let socketOpen = false;
import { websocketbaseURL } from '@/utils/configUrl'

// 发送消息
export const doSend = (message) => {
  if (socketOpen) {
    webSocket.send(message)
  }
}
 
// 初始化websocket
export const contactSocket = () => {
  if ("WebSocket" in window) {
    webSocket = new WebSocket(`ws://${websocketbaseURL}/socketApi`);
    webSocket.onopen = function () {
      console.log("连接成功！");
      socketOpen = true
    };
    webSocket.onmessage = function (evt) {
      var received_msg = evt.data;
      console.log("接受消息：" + received_msg);
    };
    webSocket.onclose = function () {
      console.log("连接关闭！");
    };
    webSocket.onerror = function () {
      console.log("连接异常！");
    };
  }
}



