import request from "@/utils/request";

// 获取
export function getQj(url,query) {
  return request({
    url,
    method: "get",
    params: query
  });
}


// 获取
export function postQj(url,query) {
  return request({
    url,
    method: "post",
    data: query
  });
}

// 获取
export function deleteQj(url,query) {
  return request({
    url,
    method: "delete",
    data: query
  });
}

// 获取
export function putQj(url,query) {
  return request({
    url,
    method: "put",
    data: query
  });
}


