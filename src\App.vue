<!--
 * @Description: 
 * @Author: 
 * @Date: 2024-03-08 20:38:49
-->
<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import '@/assets/font/font.css'
export default {
  name: 'App',
  created() {

    var token = this.$cookies.get("rb_token");
    console.log(token)
    var role = this.$cookies.get("rb_role");
    
    if(token == null || token == "" || role == null || role == null) {
      this.$router.push({path: '/' }) //跳转到首页
    }
  }
}
</script>

<style>
ul {
  list-style: none;
}
a {
  text-decoration: none;
}
* {
  margin: 0;
  padding: 0;
}
#app {
  font-family: "Microsoft YaHei", "Helvetica", "Tahoma", "Geneva", "Arial", sans-serif;
  background-color: #eee;
}
</style>
