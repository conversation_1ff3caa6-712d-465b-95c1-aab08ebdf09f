export const YZ_TYPE_LIST =[{
    text: '输液',
    value: 5
}, {
    text: '发药',
    value: 6
}, {
    text: '注射',
    value: 7
}, {
    text: '测血糖',
    value: 8
}, {
    text: '护理',
    value: 9
}, {
    text: '检验',
    value: 10
}, {
    text: '检查',
    value: 11
},{
    text: '输血',
    value: 12
},{
    text: '采血',
    value: 13
},{
    text: '治疗 ',
    value: 14
},{
    text: '手术 ',
    value: 15
},{
    text: '护理级别 ',
    value: 16
},{
    text: '饮食',
    value: 17
},{
    text: '诊断',
    value: 18
}];


export const YZ_TYPE_LIST1 =[{
    text: '输液',
    value: 5
}, {
    text: '发药',
    value: 6
}, {
    text: '注射',
    value: 7
}, {
    text: '护理',
    value: 9
}, {
    text: '检验',
    value: 10
}, {
    text: '检查',
    value: 11
},{
    text: '输血',
    value: 12
},{
    text: '治疗 ',
    value: 14
}];



export const FORMART_TYPE = ((val)=>{
    if(val){
        let findArr = YZ_TYPE_LIST.filter(item=>item.value == val);
        if(findArr.length > 0){
            return findArr[0].text
        }else{
            return ''
        }
    }else{
        return val
    }
    
});
