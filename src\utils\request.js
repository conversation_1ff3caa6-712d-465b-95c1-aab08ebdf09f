import axios from "axios";
import { baseURL } from "./configUrl";
import { Notification, MessageBox, Message } from "element-ui";
import VueCookies from 'vue-cookies'
import store from '@/vuex/store'
// import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";



let rb_token = VueCookies.get("cardId") || sessionStorage.getItem("cname");
let rb_role = VueCookies.get("role")  || sessionStorage.getItem("role");
let cname = VueCookies.get("cname")  || sessionStorage.getItem("cname");
let cid = VueCookies.get("cid")  || sessionStorage.getItem("cid");




// console.log(window.location.hostname)
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // baseURL: process.env.VUE_APP_BASE_API,
  // baseURL: `http://${window.location.hostname}:9201`,//ngnix部署用这个地址
  // baseURL: "http://*************:9201",
  baseURL: "http://*************:9201",
  // baseURL: "http://localhost:9201/",//走本地后台，绕过代理,exe部署用这个地址
  // baseURL: "/api", //走代理
  // 超时
  timeout: 15000,
});



// request拦截器
service.interceptors.request.use(
  (config) => {
    // 是否需要设置 tokenx`
    let userInfo = store.state.userInfo
    if(store.state.userInfo){
      config.headers["Authorization"] = `cardId=${userInfo.cardId};rb_token=${userInfo.cardId};rb_role=${userInfo.role};cid=${userInfo.adminId};role=${userInfo.role}`; // 让每个请求携带自定义token 请根据实际情况自行修改
    }else if(rb_token) {
      config.headers["Authorization"] = `cardId=${rb_token};rb_token=${rb_token};rb_role=${rb_role};cid=${cid};role=${rb_role}`; // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === "get" && config.params) {
      let url = config.url + "?";
      for (const propName of Object.keys(config.params)) {
        const value = config.params[propName];
        var part = encodeURIComponent(propName) + "=";
        if (value !== null && typeof value !== "undefined") {
          if (typeof value === "object") {
            for (const key of Object.keys(value)) {
              let params = propName + "[" + key + "]";
              var subPart = encodeURIComponent(params) + "=";
              url += subPart + encodeURIComponent(value[key]) + "&";
            }
          } else {
            url += part + encodeURIComponent(value) + "&";
          }
        }
      }
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    // post 表单提交
    if (config.method === "post" && config.postParams) {
      let formValues = config.postParams || {};
      let formData = new FormData();
      for (var key in formValues) {
        formData.append(key, formValues[key]);
      }
      config.headers["Content-Type"] = "multipart/form-data"; // 让每个请求携带自定义token 请根据实际情况自行修改
      config.withCredentials = true;     //加上这个会跨域
      config.data = formData;
    } else if (config.method === "post" && config.urlencoded) {
      let formValues = config.body || {};
      let formData = new FormData();
      for (var key in formValues) {
        formData.append(key, formValues[key]);
      }
      config.headers["Content-Type"] = "application/x-www-form-urlencoded"; // 让每个请求携带自定义token 请根据实际情况自行修改
      config.data = formData;
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode["default"];
    if (code === 401) {
      
    } else if (code === 500) {
      Message({
        message: msg,
        type: "error",
      });
      return Promise.reject(new Error(msg));

    } else if (code === 403) {
      // MessageBox.confirm(res.data.msg, "系统提示", {
      //   confirmButtonText: "去授权",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(() => {
      //   // location.href = "/config/licenseAuthorization?code="+res.data.cpu;
      //   location.href = "/config/licenseAuthorization";
      // }).catch(() => {});
      return Promise.reject(new Error(msg));
    }else if (code !== 200) {
      Notification.error({
        title: msg,
      });
      return Promise.reject("error");
    } else {
      return res.data;
    }


  },
  (error) => {
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }

    Message({
      message: message,
      type: "error",
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
