/**
 * 排班工具函数
 */
// import Excel from "exceljs";
/**
 * @param {一维数组} arr
 * @returns 返回数组中最小值的索引
 */
export function getIndexOfMinValue(arr) {
  if (arr.length === 0) return -1; // 如果数组为空，返回-1
  let min = Math.min(...arr); // 获取数组中的最小值
  return arr.findIndex((value) => value === min); // 返回最小值的下标
}

/**
 * 统计数组中元素出现的次数，返回对象的形式
 * @param {一维数组} nums
 * @returns {1: 1, 2: 2, 3: 1, 5: 2, 6: 1}
 */
export function getNumCount(nums) {
  const totalObj = nums.reduce((pre, next) => {
    if (pre[next]) {
      pre[next]++;
    } else {
      pre[next] = 1;
    }
    return pre;
  }, {});
  return totalObj;
}

/**
 * 统计数组中元素出现的次数，返回对象的形式
1,2,3员工，早、晚、夜的数量
// let obj={
  //   1:[2,0,0],
  //   2:[2,0,0],
  //   3:[2,0,0],
  //   4:[2,0,0],
  //   5:[0,2,0],
  // }

 */
export function getNumCount2(schedule, hasEmployeeSchedule, dayScheduleNum) {
  // schedule=[
  //   [[1,2,3,4],[5,6,7],[8,9,10]],
  //   [[1,2,3,4],[5,6,7],[8,9,10]]
  // ]
  const hasEmployeeScheduleObj = hasEmployeeSchedule.reduce(
    (accumulator, currentValue, index) => {
      accumulator[currentValue] = Array.from(
        { length: dayScheduleNum },
        () => 0
      );
      return accumulator;
    },
    {}
  );
  // console.log(hasEmployeeScheduleObj,'hasEmployeeScheduleObj=============')

  // let obj={
  //   1:[2,0,0],
  //   2:[2,0,0],
  //   3:[2,0,0],
  //   4:[2,0,0],
  //   5:[0,2,0],
  // }
  // let result={}

  schedule.map((itemDay, indexDay) => {
    itemDay.map((itemClass, indexClass) => {
      // console.log(itemClass,'itemClass======================')
      for (let key in hasEmployeeScheduleObj) {
        if (itemClass.includes(parseInt(key))) {
          hasEmployeeScheduleObj[key][indexClass]++;
        }
      }
    });
  });
  return hasEmployeeScheduleObj;
}
/**
 * 取一个数组中值，随机
 * @param {数组} arr
 * @returns  数组的值
 */
export function getRandomValue(arr) {
  var randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
}

/**
*  求2个数组的差
* @param {数组1} arr1 
* @param {数组2} arr2 
* @returns 
* // let arr1 = [1, 2, 3, 4];
   // let arr2 = [1，2];
   // let result =[3,4]
*/
export function filterArr(arr1, arr2) {
  return arr1.filter((item) => !arr2.includes(item));
}
// 打印排班表 -控制台  格式1
export function printSchedule1(schedule, days) {
  for (let day = 0; day < days; day++) {
    console.log(`第${day + 1}天:`);
    for (let shift = 0; shift < 3; shift++) {
      console.log(`  班次${shift + 1}: ${schedule[day][shift].join(", ")}`);
    }
  }
}
// 打印排班表 -控制台  格式2
export function printSchedule2(obj) {
  let arr = [];
  for (let k in obj) {
    let item = [k, ...obj[k]];
    arr.push(item);
  }
  console.log(`员工   周一    周二    周三    周四    周五    周六    周日`);
  arr.map((items) => {
    let mess = "";
    items.map((item) => {
      mess += item + "       ";
    });
    console.log(mess);
  });
}

// 格式转换，以员工为单位，方便查看
export function changeGeshi(schedule) {
  let obj = {};
  // 获取员工的键
  let arr = [...new Set(schedule.flat(Infinity))];
  arr = arr.sort((a, b) => {
    return a - b;
  });
  // 对象初始化
  arr.map((item, index) => {
    obj[item] = [];
  });
  // console.log(schedule,'schedule=================')
  // console.log(arr,'arr==============')
  schedule.map((itemDay, indexDay) => {
    itemDay.map((itemClass, indexClass) => {
      itemClass.map((itemPeople, indexPeople) => {
        obj[itemPeople].push(indexClass + 1);
      });
    });
    let other = filterArr(arr, itemDay.flat(Infinity));
    other.map((itemOther) => {
      obj[itemOther].push("休");
    });
  });
  return obj;
}
// 快速创建一个 1-n的数组
export function createEmployeeIdArray(n) {
  return Array.from({ length: n }, (_, i) => i + 1);
}

export async function createExcel(columns, rows) {
  // 创建一个新的工作簿
  let workbook = new Excel.Workbook();

  // 添加一个工作表
  let worksheet = workbook.addWorksheet("排班表");

  // 添加表头
  worksheet.columns = columns;

  // 添加数据行
  rows.map((item) => {
    worksheet.addRow(item);
  });
  // 写入文件到磁盘
  await workbook.xlsx.writeFile("myExcel.xlsx");
}
// 统计数组中有多少个数字，工作天数
export function countWordDay(arr) {
  return arr.filter((item) => typeof item === "number" && !Number.isNaN(item))
    .length;
}
// JS 对象交换顺序
// let obj={
//   "P": 3,
//   "A": 3,
//   "NA": 0,
//   "M": 0,
//   "N": 2,
//   "PN": 0,
//   "AP": 0
// }
// let order=[
//   "A",
//   "P",
//   "N",
//   "AP",
//   "PN",
//   "NA",
//   "M"
// ]
/**
 *
 * @param {待排序对象} obj
 * @param {排序数组} order
 * @returns  obj跟着 order中数组的顺序进行排序
 */
export function swapObjectOrder(obj, order) {
  // 创建一个新对象
  var newObj = {};
  // 将原始对象的属性转换为数组，并按照需要的顺序进行排序
  var keys = Object.keys(obj).sort(function (a, b) {
    return order.indexOf(a) - order.indexOf(b);
  });
  // 遍历排序后的数组，将每个属性的下标按照排序后的顺序重新赋值给新对象
  keys.forEach(function (key, index) {
    newObj[key] = obj[key];
    Object.defineProperty(newObj, key, {
      enumerable: true,
      configurable: true,
      writable: true,
      value: obj[key],
    });
  });

  // 返回新对象作为交换后的结果
  return newObj;
}
/**
 *
 * @param {1 下一周 -1表示上一周} i
 * @returns 获取上一周/下一周的当天日期
 */
export function getweekDate(i, currentDate) {
  const today = new Date(currentDate);
  const week = new Date(today);
  if (i == 1) {
    week.setDate(today.getDate() + 7);
  } else {
    week.setDate(today.getDate() - 7);
  }
  return week;
}

/**
 *
 * @param {1 下一月 -1表示上一月} i
 * @returns 获取上一个月/下一个月的当天日期
 */
export function getMonthDate(i, currentDate) {
  const today = new Date(currentDate);
  let month = "";
  if (i == 1) {
    month = new Date(today.getFullYear(), today.getMonth() + 1, 1);
  } else {
    month = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  }
  return month;
}
