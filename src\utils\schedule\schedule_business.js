/**
 * 排班-侧重业务
 */
// 统计护士各个班次的人数

/**
 * 统计护士各个班次的人数
 * @param {排班计划表} arr 
 * @returns  排班计划表  多返回这样一个数组字段，排序后的
 *   aliasCount=[
       {alias:'A',num:2,colorVal:'rgba(246, 200, 98, 0.5)'},
       {alias:'P',num:2,colorVal:'rgba(246, 200, 98, 0.5)'},
       {alias:'N',num:2,colorVal:'rgba(246, 200, 98, 0.5)'},
     ]
 */
import { Schedule } from "@/utils/schedule/schedule_algorithm.js";
export function countNurseClass(arr) {
  // 如果用深拷贝，会有bug,无法变动跟踪 排班计划的统计信息
  // let copy = JSON.parse(JSON.stringify(arr));
  let copy = arr;
  copy.map((itemDay, indexDay) => {
    let arr = [];
    itemDay.dateList.map((itemClass, indexClass) => {
      if (itemClass.alias) {
        let index = arr.findIndex((item) => item.alias == itemClass.alias);
        if (index != -1) {
          arr[index].num++;
        } else {
          let obj = { alias: "", num: 1, colorVal: "" };
          obj.alias = itemClass.alias;
          obj.colorVal = itemClass.colorVal;
          arr.push(obj);
        }
      }
    });
    arr.sort(function (a, b) {
      let nameA = a.alias.toUpperCase(); // 转换为大写字母以处理大小写
      let nameB = b.alias.toUpperCase();
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      // 名字相同，返回0保持原始顺序
      return 0;
    });
    itemDay.aliasCount = arr;
  });
  return copy;
}

/**
 * @param {班次对应的人数} multiDayStaffList
 * @param {排班的表头，日期} tableThDatas
 * @returns 根据排班数据规则，生成每日的班次人数需要表
 */
export function getRuleDemand(multiDayStaffList, tableThDatas) {
  let arr = [];
  // 班次的数量
  let multiDayStaffListLength = Object.keys(multiDayStaffList).length;
  for (let i = 0; i < tableThDatas.length; i++) {
    let arr2 = [];
    for (let j = 0; j < multiDayStaffListLength; j++) {
      let value = Object.values(multiDayStaffList)[j];
      arr2.push(value);
    }
    arr.push(arr2);
  }
  return arr;
}

export function formatDateThis(val) {
  let vals = val.replace(/\//g, "-");
  let valsArr = vals.split("-");
  return (
    valsArr[0] +
    "-" +
    (valsArr[1] < 10 ? "0" + valsArr[1] : valsArr[1]) +
    "-" +
    (valsArr[2] < 10 ? "0" + valsArr[2] : valsArr[2])
  );
}

export function formatDateDay(val) {
  let vals = val.replace(/\//g, "-");
  let valsArr = vals.split("-");
  return valsArr[2];
}

export function formarrterWeek(index) {
  let txt = "";
  switch (index) {
    case 1:
      txt = "星期一";
      break;
    case 2:
      txt = "星期二";
      break;
    case 3:
      txt = "星期三";
      break;
    case 4:
      txt = "星期四";
      break;
    case 5:
      txt = "星期五";
      break;
    case 6:
      txt = "星期六";
      break;
    case 7:
      txt = "星期日";
      break;
  }
  return txt;
}
// 排班计划按照日期排序
export function sortClassesDatas(tableData) {
  tableData.map((itemPeople) => {
    itemPeople.dateList.sort((a, b) => {
      return dataCompare(a.dateVal, b.dateVal);
    });
    return itemPeople;
  });
  return tableData;
}

// 日期大小的比较
export function dataCompare(dateStr1, dateStr2) {
  // 将字符串转换为日期对象
  let date1 = new Date(dateStr1);
  let date2 = new Date(dateStr2);
  // 比较日期
  if (date1 > date2) {
    return 1;
  } else {
    return -1;
  }
}

// 获取周
export function getWeekDays() {
  let weekDays = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"];
  return weekDays;
}

// 排班的具体校验
export function verify(rules, datas, verifyDatas, tableData, classesDatas) {
  let verifyResults = [];
  // 校验 一周/一月工作天数不超过5/22天
  verifyCheckScheduleWorkDayMore(
    rules,
    datas,
    verifyDatas,
    verifyResults,
    tableData,
    classesDatas
  );
  // 校验 每天班次的人数是否正确
  verifyCheckScheduleCount(
    rules,
    datas,
    verifyDatas,
    verifyResults,
    tableData,
    classesDatas
  );
  // 校验班次顺序
  verifyCheckScheduleOrder(
    rules,
    datas,
    verifyDatas,
    verifyResults,
    tableData,
    classesDatas
  );
  // 校验特殊情况人员
  verifyCheckScheduleSpecial(
    rules,
    datas,
    verifyDatas,
    verifyResults,
    tableData,
    classesDatas
  );
  return verifyResults;
}
// 校验一周/一月工作天数不超过5/22天;
export function verifyCheckScheduleWorkDayMore(
  rules,
  datas,
  verifyDatas,
  verifyResults,
  tableData,
  classesDatas
) {
  let weekMsg = datas.days == 7 ? "一周" : "一月";
  let message = `校验${weekMsg}工作天数不超过${rules.ifWorkDays}天`;
  const schedule3 = new Schedule(rules, datas);
  let { result, obj } = schedule3.checkScheduleWorkDayMore(
    verifyDatas,
    rules.ifWorkDays
  );
  let item = {
    result,
    message,
    spreadDetailFlag: false,
    detail: [],
  };
  if (!result) {
    for (let key in obj) {
      let index = parseInt(key);
      let employee = tableData[index - 1].name;
      let msg = `${employee}的工作天数为${obj[key]}天`;
      item.detail.push(msg);
    }
  }
  verifyResults.push(item);
  return verifyResults;
}
// 校验每天班次的人数是否正确
export function verifyCheckScheduleCount(
  rules,
  datas,
  verifyDatas,
  verifyResults,
  tableData,
  classesDatas
) {
  let message = "校验每天班次的人数是否正确";
  const schedule = new Schedule(rules, datas);
  let { result, objArr } = schedule.checkScheduleCount(
    verifyDatas,
    rules.demand
  );
  let verifyResult = {
    result,
    message,
    spreadDetailFlag: false,
    detail: [],
  };
  // 如果校验未通过
  if (!result && objArr.length > 0) {
    objArr.map((item) => {
      let day = tableData[0].dateList[item.day].dateVal;
      let aliasClass = getClassByIndex(item.aliasClass, classesDatas);
      let needNum = item.needNum;
      let realityNum = item.realityNum;
      let msg = `在${day}这一天,班次${aliasClass}需要的人数为${needNum}人,实际人数为${realityNum}人`;
      verifyResult.detail.push(msg);
    });
  }
  verifyResults.push(verifyResult);
  return verifyResults;
}
// 校验班次顺序
export function verifyCheckScheduleOrder(
  rules,
  datas,
  verifyDatas,
  verifyResults,
  tableData,
  classesDatas
) {
  let message = "校验班次的顺序是否合理";
  const schedule = new Schedule(rules, datas);
  let { result, objArr } = schedule.checkScheduleOrder(verifyDatas);
  let verifyResult = {
    result,
    message,
    spreadDetailFlag: false,
    detail: [],
  };
  if (!result && objArr.length > 0) {
    objArr.map((item) => {
      let employeeName = tableData[item.employee - 1].name;
      let day = tableData[item.employee].dateList[item.indexDay].dateVal;
      let beforeAliasName = getClassByIndex(
        item.beforeAliasIndex,
        classesDatas
      );
      let afterAliasName = getClassByIndex(item.afterAliasIndex, classesDatas);
      let msg = `在${day}这一天，${employeeName}前一天安排了${beforeAliasName}班次,今日又安排了${afterAliasName}班次`;
      verifyResult.detail.push(msg);
    });
  }
  verifyResults.push(verifyResult);
  return verifyResults;
}
// 校验特殊情况人员
export function verifyCheckScheduleSpecial(
  rules,
  datas,
  verifyDatas,
  verifyResults,
  tableData,
  classesDatas
) {
  let message = "校验特殊情况人员";
  const schedule = new Schedule(rules, datas);
  let { result, objArr } = schedule.checkScheduleSpecial(verifyDatas);
  let verifyResult = {
    result,
    message,
    spreadDetailFlag: false,
    detail: [],
  };
  if (!result && objArr.length > 0) {
    objArr.map((item) => {
      let employeeName = tableData[item.employee - 1].name;
      let day = tableData[item.employee].dateList[item.indexDay].dateVal;
      let aliasName = getClassByIndex(item.indexShift, classesDatas);
      let msg = `在${day}这一天，${employeeName}安排了${aliasName}班次,`;
      verifyResult.detail.push(msg);
    });
  }
  verifyResults.push(verifyResult);
  return verifyResults;
}
// 根据索引获得班次详细信息
export function getClassByIndex(index, classesDatas) {
  let arr = classesDatas.filter((item) => item.alias != "X");

  let obj = arr[index].alias;
  return obj;
}

