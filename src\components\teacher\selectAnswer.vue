<!-- //查询所有题库 -->
<template>
  <div class="exam">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="试卷名称">
        <el-input v-model="subject" clearable @change="inputChange" placeholder="试卷名称"></el-input>
        <!-- <el-select>
          <el-option></el-option>
        </el-select> -->
      </el-form-item>
      <!-- <el-form-item label="章节">
        <el-input v-model="section" placeholder="章节"></el-input>
      </el-form-item> -->
      <el-form-item label="题目内容">
        <el-input v-model="question" @change="inputChange1"  clearable placeholder="题目内容"></el-input>
      </el-form-item>

      <el-form-item label="题目类型">
        <el-select v-model="type" placeholder="选择题目类型" clearable class="w150">
          <el-option label="选择题" value="选择题"> </el-option>
          <el-option label="判断题" value="判断题"> </el-option>
          <el-option label="填空题" value="填空题"> </el-option>
        </el-select>
      </el-form-item><br>


      <el-form-item>
        <el-button type="primary" @click="getAnswerInfo"  style="font-size: 18px;" size="mini"> 查询</el-button>
        <!-- <el-button type="primary" @click="importTable"  style="font-size: 18px;" size="mini"> 导入</el-button> -->
        <el-button type="primary" @click="exportTable"  style="font-size: 18px;" size="mini"> 导出</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="pagination.records" border :row-class-name="tableRowClassName">
      <el-table-column fixed="left" prop="subject" label="试卷名称" width="180"></el-table-column>
      <el-table-column prop="question" label="题目信息" width="490"></el-table-column>
      <!-- <el-table-column prop="section" label="所属章节" width="200"></el-table-column> -->
      <el-table-column prop="type" label="题目类型" ></el-table-column>
      <el-table-column prop="score" label="试题分数" ></el-table-column>
      <!-- <el-table-column prop="level" label="难度等级" ></el-table-column> -->
      <el-table-column fixed="right" label="操作" width="400" fiexd="right">
        <template slot-scope="scope">
          <el-button @click="toEdit(scope.row.type, scope.row.questionId)" type="primary" size="mini"> 编辑</el-button>
          <el-button @click="toDel(scope.row.type, scope.row.questionId)" type="danger" size="mini"> 删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.current"
      :page-sizes="[6, 10]"
      :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      class="page"
    ></el-pagination>

    <!-- 用户导入对话框 v-dialogDrag  -->
    <el-dialog title="题库导入" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"     
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import { getQj , postQj, deleteQj , putQj} from "@/api/index";
  import {exportExcel} from "@/utils/zipdownload";
  import { baseURL } from '@/utils/configUrl'
export default {
  data() {
    return {
      pagination: {
        //分页后的考试信息
        current: 1, //当前页
        total: null, //记录条数
        size: 6 //每页条数

      },
      section: "",
      subject: "",
      question: "",
      type:'',
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部 "Bearer " + getToken() 
        headers: { Authorization: "Bearer "},
        // 上传的地址
        url: baseURL + "/system/user/importData"
      },
    };
  },
  created() {
    this.subject = sessionStorage.getItem('s_subject') || ''; 
    this.question = sessionStorage.getItem('s_question') || ''; 
    this.getAnswerInfo();
  },
  methods: {
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    exportTable(){
      this.$confirm('是否确认导出所有试题列表?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal:false,
        type: "warning"
      }).then(() => {
        let fileName = '试题列表' + Date.now();
        // 开始导出
        exportExcel("/exportZip", {},fileName);
      }).catch(() => {});
    },
    importTable(){
      console.log("导入")
      this.upload.open = true;
    },
    inputChange(val){
      sessionStorage.setItem('s_subject', val); // 存入一个值
    },
    inputChange1(val){
      sessionStorage.setItem('s_question', val); // 存入一个值
    },
    toDel(type, id) { 
      let that = this;
      this.$confirm('是否确认删除该题目吗?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal:false,
        type: "warning"
      }).then(function() {
        getQj(`answers/delete?questionId=${id}&type=${type}`).then(res => {
          that.$message.success('删除成功！');
          that.getAnswerInfo();
        }).catch(error => {});
      }).catch(error => {});
    },
    toEdit(type, id) { 
      // 前往编辑题目
      this.$router.push({path:'/editAnswerChildren',query: {type: type, questionId: id}})
    },
    getAnswerInfo(size, current) {
      //分页查询所有试卷信息
      if(typeof size === 'number' && !isNaN(size)) {
        this.pagination.size = size;
      }
      if(typeof current === 'number' && !isNaN(current)) {
        this.pagination.current = current;
      } else {
        this.pagination.current = 1;
      }
      // var subject = this.subject;
      // if(this.subject.trim() == "") {
      //   subject = "@";
      // }
      // var section = this.section;
      // if(this.section.trim() == "") {
      //   section = "@";
      // }
      // var question = this.question;
      // if(this.question.trim() == "") {
      //   question = "@";
      // }
      getQj( `answers/findAllQuestion`,{
        page:this.pagination.current,
        size:this.pagination.size,
        subject:this.subject,
        section:this.section,
        question:this.question,
        type:this.type
      }).then(res => {
        this.pagination = res.data;
      }).catch(error => {});

      // getQj( `/answers?current=${this.pagination.current}&size=${this.pagination.size}&subject=${subject}&section=${section}&question=${question}&type=${this.type}` ).then(res => {
      //   this.pagination = res.data;
      // }).catch(error => {});
    },
    //改变当前记录条数
    handleSizeChange(val) {
      this.getAnswerInfo(val);
    },
    //改变当前页码，重新发送请求
    handleCurrentChange(val) {
      this.getAnswerInfo(undefined, val);
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 == 0) {
        return "warning-row";
      } else {
        return "success-row";
      }
    }
  }
};
</script>
<style lang="less" scoped>
.exam {
  padding: 0px 40px;
  .page {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edit {
    margin-left: 20px;
  }
  .el-table tr {
    background-color: #DD5862 !important;
  }
}
  .el-table .warning-row {
    background: #000 !important;
    
  }

  .el-table .success-row {
    background: #DD5862;
  }

</style>
