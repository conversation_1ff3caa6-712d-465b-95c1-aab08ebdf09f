<!-- 用户登录界面 -->
<template>
    <div id="login">
        <div class="bg">
            <!-- <img src="@/assets/img/loginbg.jpg" alt="" /> -->
        </div>
        <el-row class="main-container">
            <el-col :lg="8" :xs="16" :md="10" :span="10">
                <div class="top" style="color: black;font-size: 32px">
                    <!-- <i class="iconfont icon-r-team title-icon"></i>  -->
                     <img style="width: 40px;height: 50px;margin-right: 5px;" src="@/assets/logo1.png" alt="" />
                    在线考试系统 
                    <span class="title"> </span>
                </div>
                <br />
                <div class="bottom">
                    <div class="container">
                        <p class="title">账号登录</p>
                        <el-form :label-position="labelPosition" label-width="60px" :model="formLabelAlign">
                            <el-form-item label="用户名">
                                <el-input v-model.number="formLabelAlign.username" placeholder="请输入用户名" @keyup.enter.native="login"></el-input>
                            </el-form-item>
                            <el-form-item label="密   码">
                                <el-input v-model="formLabelAlign.password" placeholder="请输入密码"
                                    type="password" @keyup.enter.native="login"></el-input>
                            </el-form-item>
                            <div class="submit">
                                <el-button type="primary" class="row-login"  @click.enter.native="login"  size="mini"> 登录</el-button>
                            </div>
                        </el-form>
                    </div>
                </div>
            </el-col>
        </el-row>
        <el-row class="footer">
            <el-col>
            </el-col>
        </el-row>
    </div>
</template>

<script>
    import { getQj , postQj, deleteQj , putQj} from "@/api/index";
import { mapState } from "vuex";
export default {
    name: "login",
    data() {
        return {
            role: 2,
            labelPosition: "left",
            formLabelAlign: {
                username: "admin",
                password: "",
            },
        };
    },
    methods: {
        // getLocalIP() {
        //     this.localIP = '127.0.0.1'; // 假设IP地址，实际应用中需要通过外部服务或Web API获取
        //     // 使用Web API获取
        //     // 注意：此操作可能受到浏览器安全策略的限制，某些情况下可能无法获取
        //     navigator.mediaDevices.getUserMedia({ audio: true })
        //         .then(stream => {
        //         const audioTrack = stream.getAudioTracks()[0];
        //         const ipAddress = audioTrack.getSettings().deviceId.split(':')[1];
        //         this.localIP = ipAddress;
        //         })
        //         .catch(error => {
        //         console.error('获取本机IP地址失败:', error);
        //         this.localIP = '无法获取';
        //         });
        // },
        // getIpUrl(){
        //     //获取本机的IP地址
        //     let ip = 'localhost';
        //     const network = Os.networkInterfaces();//获取本机的网路
        //     for (const iter in network) {
        //         let faces = network[iter];
        //         for (let item of faces) {
        //             if (item.family === 'IPv4' && item.address !== '127.0.0.1' && !item.internal) {
        //                 ip = item.address;
        //                 return ip;
        //             }
        //         }
        //     }
        //     console.log(ip)
        //     return ip;
        // },
        //用户登录请求后台处理
        login() {
            if (
                this.formLabelAlign.username == undefined ||
                this.formLabelAlign.username == ""
            ) {
                this.$message("请输入用户名");
                return;
            }
            // if (
            //     !/^\d+$/.test(this.formLabelAlign.username) ||
            //     this.formLabelAlign.username.toString().length > 10
            // ) {
            //     this.$message("用户名有误");
            //     return;
            // }
            if (this.formLabelAlign.password == "") {
                this.$message("请输入密码");
                return;
            }

            // ${this.$baseURL}  `api/login`
            
            postQj(`/login`,
                {
                    ...this.formLabelAlign,
                },
            ).then((res) => {
                let resData = res.data;
                if (resData != null) {
                    this.$cookies.set("rb_token", resData.cardId);
                    this.$cookies.set("rb_role", resData.role);
                    sessionStorage.setItem("cname", resData.adminName);
                    sessionStorage.setItem("cid", resData.adminId);
                    sessionStorage.setItem("role", resData.role);
                    sessionStorage.setItem("cardId", resData.cardId);
                    this.$store.commit('SET_USERINFO',resData);
                    switch (resData.role) {
                        case "0": //管理员
                            this.$cookies.set("cname", resData.adminName);
                            this.$cookies.set("cid", resData.adminId);
                            this.$cookies.set("role", 0);
                            this.$cookies.set("cardId", resData.cardId);
                            this.$router.push({ path: "/index" }); //跳转到首页
                            break;
                        case "1": //考官
                            this.$cookies.set("cname", resData.teacherName);
                            this.$cookies.set("cid", resData.teacherId);
                            this.$cookies.set("cardId", resData.cardId);
                            this.$cookies.set("role", 1);
                            this.$router.push({ path: "/index" }); //跳转到教师用户
                            break;
                        case "2": //考生
                            this.$cookies.set("cname", resData.studentName);
                            this.$cookies.set("cid", resData.studentId);
                            this.$cookies.set("cardId", resData.cardId);
                            this.$router.push({ path: "/student" });
                            break;
                    }
                }else{
                    //错误提示
                    this.$message({
                        showClose: true,
                        type: "error",
                        message: "用户名或者密码错误",
                    });
                }
            }).catch((e) => {
                console.log(e);
                if (
                    e.response == undefined ||
                    e.response.data == undefined
                ) {
                    this.$message({
                        showClose: true,
                        message: e,
                        type: "error",
                        duration: 5000,
                    });
                } else {
                    this.$message({
                        showClose: true,
                        message: e.response.data,
                        type: "error",
                        duration: 5000,
                    });
                }
            });
        },
        clickTag(key) {
            this.role = key;
        },
    },
    computed: mapState(["userInfo"]),
    mounted() {
        // this.getLocalIP();
        // console.log(this.$baseURL)
     },
};
</script> 

<style lang="less" scoped>
.remind {
    border-radius: 4px;
    padding: 10px 20px;
    display: flex;
    position: fixed;
    right: 20px;
    bottom: 50%;
    flex-direction: column;
    color: #606266;
    background-color: #fff;
    border-left: 4px solid #409eff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.container {
    margin-bottom: 32px;
}

.title-icon {
    font-size: 44px;
    margin-right: 10px;
}

.container .el-radio-group {
    margin: 30px 0px;
}

a:link {
    color: #ff962a;
    text-decoration: none;
}

#login {
    font-size: 14px;
    color: #000;
    background-color: #fff;
}

#login .bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    overflow-y: hidden;
    height: 100%;
    /* background: url("../../assets/img/loginbg.jpg") center top / cover no-repeat; */
    background: #A8B9EF;
    background-color: #A8B9EF !important;
}
#login .bg  img{
    width: 100%;
    height: 100vh;
}
#login .main-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

#login .main-container .top {
    margin-top: 100px;
    font-size: 30px;
    color: #ff962a;
    display: flex;
    justify-content: center;
}

#login .top .icon-kaoshi {
    font-size: 80px;
}

#login .top .title {
    margin-top: 20px;
}

#login .bottom {
    display: flex;
    justify-content: center;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

#login .bottom .title {
    text-align: center;
    font-size: 30px;
}

.bottom .container .title {
    margin: 30px 0px;
}

.bottom .submit .row-login {
    width: 100%;
    margin: 20px 0px 10px 0px;
    padding: 15px 20px;
    background-color: rgb(133, 174, 191);
    border-color: rgb(133, 174, 191);
    color: white
}

.bottom .submit {
    display: flex;
    justify-content: center;
}

.footer {
    margin-top: 50px;
    text-align: center;
}

.footer .msg1 {
    font-size: 18px;
    color: #fff;
    margin-bottom: 15px;
}

.footer .msg2 {
    font-size: 14px;
    color: #e3e3e3;
    margin-top: 70px;
}

.bottom .options {
    margin-bottom: 40px;
    color: #ff962a;
    display: flex;
    justify-content: space-between;
}

.bottom .options>a {
    color: #ff962a;
}

.bottom .options .register span:nth-child(1) {
    color: #8c8c8c;
}
</style>
