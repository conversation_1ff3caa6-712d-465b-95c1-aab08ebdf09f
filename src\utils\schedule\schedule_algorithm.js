/**
 * 排班算法
 */
import {
  getIndexOfMinValue,
  getNumCount,
  getNumCount2,
  getRandomValue,
  printSchedule1,
  printSchedule2,
  changeGeshi,
  createEmployeeIdArray,
  createExcel,
  filterArr,
  countWordDay,
} from "./schedule_tools.js";
/**说明 排一周的班
 * 护士排班的约束条件：
 *
 * 1.一人一天排一个班次。
 * 2.前一天上完夜班后，第二天不要安排早班。
 * 3.一周/一月工作天数不超过5/22天，可以自由设置。
 * 4.每个人尽量平均分配工作。
 * 5.可以根据班次的需求人数排班。
 */
// // 程序入口
// async function start() {
//   // 初始化数据:
//   // 排班需求、人员列表,排班表
//   let { demand, employeeIds, schedule } = initData();
//   // 生成排班表
//   startSchedule(schedule, demand, employeeIds);
//   // 打印排班表 -格式1
//   printSchedule1(schedule, days);
//   // 打印排班表 -格式2
//   printSchedule2(changeGeshi(schedule));
//   // 导出排班表excel
//   exportExcel(days, schedule);
// }

export class Schedule {
  // 约束条件
  // 人员数量:
  // 一周的工作：所有班次之和/5+1 ,
  // （99*4+30）/22 +1
  employeeNum = 0;
  // 排班的周期
  days = 0;
  // 一天有几个班次
  dayScheduleNum = 0;
  // 算法安排尝试的次数
  runNum = 0;
  // 工作天数不超过
  ifWorkDays = 0;
  // 班次需求
  demand = [];
  // 全局的班次
  schedule = [];
  // 排班详细规则
  extRule = {};
  // 失败次数
  failnum = 0;
  constructor(rules, datas) {
    let { employeeNum, days, dayScheduleNum, hasSchedule, scheduleHoliday } =
      datas;
    let { runNum, ifWorkDays, demand, extRule } = rules;
    this.employeeNum = employeeNum;
    this.days = days;
    this.dayScheduleNum = dayScheduleNum;
    this.runNum = runNum;
    this.ifWorkDays = ifWorkDays;
    this.demand = demand;
    this.extRule = extRule;
    this.hasSchedule = hasSchedule;
    this.scheduleHoliday = scheduleHoliday;
  }
  initData(employeeNum, days, dayScheduleNum) {
    // 员工序号
    let employeeIds = createEmployeeIdArray(employeeNum);
    // 排班表，初始化为空
    const schedule = Array.from({ length: days }, () =>
      Array(dayScheduleNum)
        .fill(0)
        .map(() => [])
    );
    this.failnum = 0;
    this.schedule = this.extRule.rule3.hasScheduleDataFlag
      ? this.hasSchedule
      : schedule;
    return { employeeIds };
  }
  // 生成排班表
  startSchedule(demand, employeeIds, days, dayScheduleNum) {
    let result = {
      ok: true, // true 成功，false 失败
      schedule: this.schedule,
      message: "智能排班生成成功",
    };
    for (let day = 0; day < days; day++) {
      for (let shift = 0; shift < dayScheduleNum; shift++) {
        while (this.schedule[day][shift].length < demand[day][shift]) {
          if (!this.assignEmployee3(day, shift, employeeIds, dayScheduleNum)) {
            result = {
              ok: false, // true 成功，false 失败
              schedule: this.schedule,
              message: "无法满足排班要求",
            };
            return result;
          }
        }
      }
    }
    result.schedule = changeGeshi(this.schedule);
    return result;
  }
  //
  getAvarageClass(employeeIds, day, shift) {
    // this.schedule=[
    //   [[1,2,3],[4,5],[7,8]],
    //   [[1,2,3],[4,5],[7,8]],
    //   [[1,2,3],[4,5],[7,8]],
    //   [[],[],[]]
    //   [[],[],[]]
    // ]

    let obj = {
      1: [2, 1, 1],
      2: [2, 1, 2],
      3: [0, 1, 1],
    };
    let arr = [];
    for (let key in obj) {
      arr.push(obj[key]);
    }
    let no = Math.min(arr);
    let index = arr.findIndex((item) => item == no);
    return index + 1;
    // this.schedule

    // 3. 每个员工的班次尽量平均分配
    try {
      if (day >= 3) {
        // APN的数量
        let arr = [0, 0, 0];
        // day 4 i 0    0,1,2,3

        // day 5 i 1    1,2,3,4
        // day 6 i 2    2,3,4,5

        // day 30
        for (let i = day - 3; i < day; i++) {
          // this.schedule=[
          //   [[1,2,3],[4,5],[7,8]],
          //   [[1,2,3],[4,5],[7,8]],
          //   [[1,2,3],[4,5],[7,8]],
          //   [[],[],[]]
          //   [[],[],[]]
          // ]

          let dayArr = this.schedule[i];
          dayArr.map((itemArray, index) => {
            if (itemArray.includes(employee)) {
              arr[index] = arr[index] + 1;
            }
          });
          // console.log(arr,'arr-----------------------')
        }
        // console.log(arr, '==============一个员工上四天的班次数量')
        // 统计一个人 上五天的 班次的数量
        // let arr=[2,2,1]
        if (arr[shift] >= 2) {
          console.log(arr, "arr=======");
          console.log(this.schedule, "排班表");
          console.log(employee, "员工编号");
          console.log(day, "天数");
          // return false
        }
      }
    } catch (error) {
      console.log(error);
    }
  }

  // 安排员工上班
  assignEmployee(day, shift, employeeIds, runNum) {
    for (let i = 0; i < runNum; i++) {
      if (i % 2 != 0) {
        // 第一步，先优先安排，班次最少的人，
        let e = this.getMinCount(employeeIds);
        if (
          e &&
          this.canAssign(
            day,
            shift,
            e,
            this.dayScheduleNum,
            this.days,
            this.ifWorkDays
          )
        ) {
          this.schedule[day][shift].push(e);
          return true; // 成功安排员工
        }
      } else {
        // 第二步，如果上面排班失败，则随机选取人员安排
        // 随机的员工排班，
        let e = getRandomValue(employeeIds);
        if (
          this.canAssign(
            day,
            shift,
            e,
            this.dayScheduleNum,
            this.days,
            this.ifWorkDays
          )
        ) {
          this.schedule[day][shift].push(e);
          return true; // 成功安排员工
        }
      }
      // 第三步，如果前面2步都失败的情况下，则循环尝试多次
    }
    return false; // 无法安排员工
  }
  // 数组随机排序
  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
  // 安排员工上班2
  assignEmployee2(day, shift, employeeIds, dayScheduleNum) {
    // 优先第一步，排班次数量最少的人员(已去掉，尽量保证把人员班次的连贯性，符合人的习惯，所以这里去掉了)
    let e = 0;
    let { employee, minEmployeeArr } = this.getMinCount2(
      employeeIds,
      shift,
      dayScheduleNum
    );
    if (minEmployeeArr.length > 0) {
      for (let i = 0; i < minEmployeeArr.length; i++) {
        e = minEmployeeArr[i];
        let res = this.canAssign(
          day,
          shift,
          e,
          this.dayScheduleNum,
          this.days,
          this.ifWorkDays
        );
        if (e && res) {
          this.schedule[day][shift].push(e);
          return true; // 成功安排员工
        }
      }
    } else {
      e = employee;
      let res = this.canAssign(
        day,
        shift,
        e,
        this.dayScheduleNum,
        this.days,
        this.ifWorkDays
      );
      if (e && res) {
        this.schedule[day][shift].push(e);
        return true; // 成功安排员工
      }
    }

    //否则第二步， 安排总班次最少的人，
    e = this.getMinCount(employeeIds);
    if (
      e &&
      this.canAssign(
        day,
        shift,
        e,
        this.dayScheduleNum,
        this.days,
        this.ifWorkDays
      )
    ) {
      this.schedule[day][shift].push(e);
      return true; // 成功安排员工
    }

    // 否则第三步， 随机安排人员------------
    let employeeIdsCopy = this.shuffleArray(
      JSON.parse(JSON.stringify(employeeIds))
    );
    for (let i = 0; i < employeeIdsCopy.length; i++) {
      e = employeeIdsCopy[i];
      let res2 = this.canAssign(
        day,
        shift,
        e,
        this.dayScheduleNum,
        this.days,
        this.ifWorkDays
      );
      if (res2) {
        this.schedule[day][shift].push(e);
        return true; // 成功安排员工
      }
    }
    return false; // 无法安排员工
  }

  // 安排员工上班3
  assignEmployee3(day, shift, employeeIds, dayScheduleNum) {
    // 优先第一步，排班次数量最少的人员
    let e = 0;
    // let { employee, minEmployeeArr } = this.getMinCount2(
    //   employeeIds,
    //   shift,
    //   dayScheduleNum
    // );
    // if (minEmployeeArr.length > 0) {
    //   for (let i = 0; i < minEmployeeArr.length; i++) {
    //     e = minEmployeeArr[i];
    //     let res = this.canAssign(
    //       day,
    //       shift,
    //       e,
    //       this.dayScheduleNum,
    //       this.days,
    //       this.ifWorkDays
    //     );
    //     if (e && res) {
    //       this.schedule[day][shift].push(e);
    //       return true; // 成功安排员工
    //     }
    //   }
    // } else {
    //   e = employee;
    //   let res = this.canAssign(
    //     day,
    //     shift,
    //     e,
    //     this.dayScheduleNum,
    //     this.days,
    //     this.ifWorkDays
    //   );
    //   if (e && res) {
    //     this.schedule[day][shift].push(e);
    //     return true; // 成功安排员工
    //   }
    // }

    //否则第二步， 安排总班次最少的人，
    // e = this.getMinCount(employeeIds);
    // if (
    //   e &&
    //   this.canAssign(
    //     day,
    //     shift,
    //     e,
    //     this.dayScheduleNum,
    //     this.days,
    //     this.ifWorkDays
    //   )
    // ) {
    //   this.schedule[day][shift].push(e);
    //   return true; // 成功安排员工
    // }

    // 否则第三步， 随机安排人员------------
    let employeeIdsCopy = this.shuffleArray(
      JSON.parse(JSON.stringify(employeeIds))
    );
    for (let i = 0; i < employeeIdsCopy.length; i++) {
      e = employeeIdsCopy[i];
      let res2 = this.canAssign(
        day,
        shift,
        e,
        this.dayScheduleNum,
        this.days,
        this.ifWorkDays
      );
      if (res2) {
        this.schedule[day][shift].push(e);
        return true; // 成功安排员工
      }
    }
    return false; // 无法安排员工
  }
  // 排班入口，导出外面
  entrySchedule() {
    let result = {
      ok: true, // true 成功，false 失败
      schedule: this.schedule,
      message: "",
    };
    try {
      //初始化数据
      let { employeeIds } = this.initData(
        this.employeeNum,
        this.days,
        this.dayScheduleNum
      );
      this.employeeIds = employeeIds;
      // 生成排班表
      result = this.startSchedule(
        this.demand,
        employeeIds,
        this.days,
        this.dayScheduleNum
      );
    } catch (err) {
      result.ok = false;
      result.message = err;
    } finally {
    }
    return result;
  }
  /**
   * 排班次数最少的员工
   * @returns 排班次数最少的员工
   */
  getMinCount(employeeIds) {
    // 如果才开始排，就默认选择第一个
    let employee = 1;
    let scheduleOneArr = this.schedule.flat(Infinity);
    if (scheduleOneArr.length == 0) {
      return employee;
    }
    let hasEmployeeSchedule = [...new Set(scheduleOneArr)];

    // 如果所有员工都已经排过至少一次的班了
    if (hasEmployeeSchedule.length == employeeIds.length) {
      let result = getNumCount(scheduleOneArr); // {1: 2, 2: 2, 3: 3, 4: 3, 5: 3}
      let minIndex = getIndexOfMinValue(Object.values(result));
      employee = parseInt(Object.keys(result)[minIndex]);
    } else {
      // 在未排班的人员里面随机选择一个
      let noHasEmployeeSchedule = filterArr(employeeIds, hasEmployeeSchedule);
      employee = getRandomValue(noHasEmployeeSchedule);
    }
    return employee;
  }
  /**
   * 排班次数最少的员工
   * @returns 排班次数最少的员工
   */
  getMinCount2(employeeIds, shift, dayScheduleNum) {
    // 如果才开始排，就默认选择第一个
    let employee = 1;
    // 当前班次人数最小的人员数组
    let minEmployeeArr = [];
    let scheduleOneArr = this.schedule.flat(Infinity);
    // 如果才开始，默认第一个
    if (scheduleOneArr.length == 0) {
      return { employee, minEmployeeArr };
    }
    let hasEmployeeSchedule = [...new Set(scheduleOneArr)];

    // 如果所有员工都已经排过至少一次的班了
    if (hasEmployeeSchedule.length == employeeIds.length) {
      // let obj={
      //   1:[2,0,0],
      //   2:[2,0,0],
      //   3:[1,0,0],
      //   4:[1,0,0],
      //   5:[0,2,0],
      //   6:[0,2,0],
      // }
      let result = getNumCount2(
        this.schedule,
        hasEmployeeSchedule,
        dayScheduleNum
      );
      // let min=result['1'][shift]
      // let tmp=0
      // employee=1

      // [2,2,1,1,0,0]
      let tmpArr = [];
      for (let key in result) {
        tmpArr.push(result[key][shift]);
      }
      // 0
      let tmp2 = Math.min(...tmpArr);
      for (let key in result) {
        if (result[key][shift] == tmp2) {
          minEmployeeArr.push(parseInt(key));
        }
      }
      // if(minEmployeeArr.length){
      //   console.log(minEmployeeArr)
      // }
      employee = getRandomValue(minEmployeeArr);
      // for(let key in result){
      //   tmp=result[key][shift]
      //   if(tmp<=min){
      //     min=tmp
      //     employee=parseInt(key)
      //   }
      // }
      // console.log(result, employee, '==================================================结果')
      // let minIndex = getIndexOfMinValue(Object.values(result));
      // employee = parseInt(Object.keys(result)[minIndex]);
    } else {
      // 在未排班的人员里面随机选择一个
      let noHasEmployeeSchedule = filterArr(employeeIds, hasEmployeeSchedule);
      employee = getRandomValue(noHasEmployeeSchedule);
    }
    return { employee, minEmployeeArr };
  }

  sumArray(arr) {
    return arr.reduce((sum, current) => sum + current, 0);
  }
  // 检查是否可以安排员工上班
  canAssign(day, shift, employee, dayScheduleNum, days, ifWorkDays) {
    // 1. 检查员工当天是否已经安排了班次
    for (let s = 0; s < dayScheduleNum; s++) {
      if (this.schedule[day][s].includes(employee)) return false;
    }
    // 2. 检查前一天晚班和第二天的夜班冲突
    for (let i = 0; i < this.extRule.rule1.length; i++) {
      // this.extRule.rule1
      let after = this.extRule.rule1[i].after;
      let before = this.extRule.rule1[i].before;
      if (
        after.includes(shift) &&
        day > 0 &&
        this.schedule[day - 1][before].includes(employee)
      ) {
        return false;
      }
    }

    // 3. 特殊情况人员排班
    for (let i = 0; i < this.extRule.rule2.length; i++) {
      let noclass = this.extRule.rule2[i].noclass;
      let nurse = this.extRule.rule2[i].nurse;
      // noclass
      // nurse
      let flag = noclass.includes(shift) && nurse.includes(employee);
      if (flag) {
        return false;
      }
    }
    // 4. 检查当前员工是否在休假
    if (
      this.extRule.rule3.hasScheduleDataFlag &&
      this.scheduleHoliday[day].includes(employee)
    ) {
      return false;
    }
    // this.failnum < 1000
    // 5、保证人员班次的连贯性   false 分散 true 连贯
    if (this.extRule.rule4.disperseSwitch) {
      if (day > 0) {
        // 5.1 今日的班次与昨日的班次不相同的话就不算通过
        let employeeOther = this.schedule[day - 1].flat(Infinity);
        let lastClassFlag = this.schedule[day - 1][shift].includes(employee);
        if (employeeOther.includes(employee) && !lastClassFlag) {
          return false;
        }
        // 5.2 一个的班次类别不要超过 3 过
        // let types = [0, 0, 0, 0, 0, 0];
        // // this.schedule.
        // this.schedule.map((items, indexs) => {
        //   items.map((item, index) => {
        //     if (item.includes(employee)) {
        //       types[index] = 1;
        //     }
        //   });
        // });
        // let sum = this.sumArray(types);
        // if (sum >= 7) {
        //   console.log(sum, "==============================9999");
        //   return false;
        // }
      }
    }

    // 6、不要连着上超过7天的班
    // if (day > 6) {
    //   let arr = [];
    //   // 18 17 16, 15,14 ,13 ,12
    //   let i = 0;
    //   while (i < 7) {
    //     day = day - 1;
    //     i = i + 1;
    //     let employeeOther = this.schedule[day].flat(Infinity);
    //     arr = arr.concat(employeeOther);
    //   }
    //   let result = getNumCount(arr); // {1: 2, 2: 2, 3: 3, 4: 3, 5: 3}
    //   // console.log(result[employee], "===============");
    //   if (result[employee] >= 7) {
    //     return false;
    //   }
    // }

    //
    //  A0 P1 N2
    // if (shift === 0 && day > 0 && this.schedule[day - 1][2].includes(employee)) {
    //   return false;
    // }
    // if (shift === 2 && day > 0 && this.schedule[day - 1][1].includes(employee)) {
    //   return false
    // }
    // if ((shift === 2 || shift === 0) && day > 0 && this.schedule[day - 1][1].includes(employee)) {
    //   return false
    // }

    // 3. 每个员工的班次尽量平均分配
    // try {
    //   if (day >= 2) {
    //     // APN的数量
    //     // let arr = [0, 0, 0]
    //     let arr = Array.from({ length: dayScheduleNum }, () => 0);
    //     // day 4 i 0    0,1,2,3

    //     // day 5 i 1    1,2,3,4
    //     // day 6 i 2    2,3,4,5

    //     // day 30
    //     for (let i = day - 2; i < day; i++) {
    //       // this.schedule=[
    //       //   [[1,2,3],[4,5],[7,8]],
    //       //   [[1,2,3],[4,5],[7,8]],
    //       //   [[1,2,3],[4,5],[7,8]],
    //       //   [[],[],[]]
    //       //   [[],[],[]]
    //       // ]

    //       let dayArr = this.schedule[i];
    //       dayArr.map((itemArray, index) => {
    //         if (itemArray.includes(employee)) {
    //           arr[index] = arr[index] + 1;
    //         }
    //       });
    //       // console.log(arr,'arr-----------------------')
    //     }
    //     // console.log(arr, '==============一个员工上四天的班次数量')
    //     // 统计一个人 上五天的 班次的数量
    //     // let arr=[2,3,1]
    //     // this.demand[]
    //     if (arr[shift] >= 2) {
    //       // console.log(arr, 'arr=======')
    //       // console.log(this.schedule, '排班表')
    //       // console.log(employee, '员工编号')
    //       // console.log(day, '天数')
    //       return false;
    //     }
    //   }
    // } catch (error) {
    //   console.log(error);
    // }

    // 4. 检查一周工作天数不超过 ifWorkDays 天
    let workDays = 0;
    for (let d = 0; d < days; d++) {
      for (let s = 0; s < dayScheduleNum; s++) {
        if (this.schedule[d][s].includes(employee)) workDays++;
      }
    }
    if (workDays >= ifWorkDays) return false;
    return true;
  }

  countOccurrences(array, element) {
    return array.reduce((count, current) => {
      return current === element ? count + 1 : count;
    }, 0);
  }
  // 导出排班表excel;
  async exportExcel(days, schedule) {
    let columns = [{ header: "姓名", key: "name" }];
    // 表头
    for (let i = 1; i < days + 1; i++) {
      let obj = { header: i + "", key: i + "" };
      columns.push(obj);
    }
    columns.push({
      header: "总工作天数",
      key: "sum",
    });
    let scheduleArr = changeGeshi(schedule);
    let rows = [];
    for (let key in scheduleArr) {
      let obj = {
        name: key,
      };
      scheduleArr[key].map((item, index) => {
        obj[index + 1] = item;
      });
      obj.sum = countWordDay(scheduleArr[key]);
      rows.push(obj);
    }
    await createExcel(columns, rows);
  }

  // 校验每天班次的人数是否正确
  checkScheduleCount(schedule, demand) {
    let result = true;
    let objArr = [];
    schedule.map((scheduleDay, indexDay) => {
      scheduleDay.map((scheduleClass, indexClass) => {
        let flag =
          demand[indexDay][indexClass] != 0 &&
          scheduleClass.length != demand[indexDay][indexClass];
        if (flag) {
          let obj = {
            day: indexDay,
            aliasClass: indexClass,
            needNum: demand[indexDay][indexClass],
            realityNum: scheduleClass.length,
          };
          objArr.push(obj);
          result = false;
        }
      });
    });
    return { result, objArr };
  }
  // 校验 同一个护士前一天夜班和当天早班冲突
  // false { '4': [ 12 ], '5': [ 12 ] }
  // 是否成功， 第四天，12号员工  第五天 12号 员工 上了白天后，又接着上夜班
  checkSchedulePandA(schedule) {
    try {
      let result = true;
      let obj = {};
      schedule.map((itemDay, indexDay) => {
        itemDay.map((itemShift, indexShift) => {
          itemShift.map((employee, index) => {
            // 当前的第一个班次，且不是第一天的员工，如果在前一天的第三个班次已经被安排过了
            // P班与N班
            // let flag1 = indexShift === 2 && indexDay > 0 && schedule[indexDay - 1][1].includes(employee)
            // let flag2 = (indexShift === 2||indexShift === 0) && indexDay > 0 && schedule[indexDay - 1][1].includes(employee)
            // let flag1 = false
            // if (flag1) {
            //   if (obj[indexDay + 1] && obj[indexDay + 1].length > 0) {
            //     obj[indexDay + 1].push(employee);
            //   } else {
            //     obj[indexDay + 1] = [employee];
            //   }

            //   result = false;
            // }

            for (let i = 0; i < this.extRule.rule1.length; i++) {
              // this.extRule.rule1
              let after = this.extRule.rule1[i].after;
              let before = this.extRule.rule1[i].before;
              // console.log(indexDay,indexShift,after,before,employee,'-----------------')
              if (
                after.includes(indexShift) &&
                indexDay > 0 &&
                schedule[indexDay - 1][before].includes(employee)
              ) {
                if (obj[indexDay + 1] && obj[indexDay + 1].length > 0) {
                  obj[indexDay + 1].push(employee);
                } else {
                  obj[indexDay + 1] = [employee];
                }
                result = false;
              }
            }
          });
        });
      });
      return { result, obj };
    } catch (error) {
      console.log(error);
      // console.log(this.schedule)
    }
  }

  // 校验 班次顺序，根据设定规则
  checkScheduleOrder(schedule) {
    try {
      let result = true;
      let objArr = [];
      schedule.map((itemDay, indexDay) => {
        itemDay.map((itemShift, indexShift) => {
          itemShift.map((employee, index) => {
            for (let i = 0; i < this.extRule.rule1.length; i++) {
              let after = this.extRule.rule1[i].after;
              let before = this.extRule.rule1[i].before;

              let flag =
                after.includes(indexShift) &&
                indexDay > 0 &&
                schedule[indexDay - 1][before].includes(employee);

              if (flag) {
                //XX人， 在这一天，前一天安排了XX班次，今天又安排了X班次。
                let objItem = {
                  employee,
                  indexDay,
                  beforeAliasIndex: before,
                  afterAliasIndex: indexShift,
                };
                objArr.push(objItem);
                result = false;
              }
            }
          });
        });
      });
      return { result, objArr };
    } catch (error) {
      console.log(error);
    }
  }
  // 校验 特殊情况人员，根据设定规则
  checkScheduleSpecial(schedule) {
    try {
      let result = true;
      let objArr = [];
      schedule.map((itemDay, indexDay) => {
        itemDay.map((itemShift, indexShift) => {
          itemShift.map((employee, index) => {
            for (let i = 0; i < this.extRule.rule2.length; i++) {
              //rule2= [{\"id\":0,\"nurse\":[3],\"noclass\":[2]}]
              let nurse = this.extRule.rule2[i].nurse;
              let noclass = this.extRule.rule2[i].noclass;
              let flag =
                nurse.includes(employee) && noclass.includes(indexShift);

              if (flag) {
                // XX人员 在XX天，安排了 X 班次
                let objItem = {
                  employee,
                  indexDay,
                  indexShift,
                };
                objArr.push(objItem);
                result = false;
              }
            }
          });
        });
      });
      return { result, objArr };
    } catch (error) {
      console.log(error);
    }
  }

  // 校验，一周/一月工作天数不超过5/22天
  // false { '7': 6, '11': 7, '12': 7 }
  // 员工 7,11,12 工作天数超标
  checkScheduleWorkDayMore(schedule, day) {
    let result = true;
    let obj = {};
    let scheduleArr = changeGeshi(schedule);
    for (let key in scheduleArr) {
      let sum = countWordDay(scheduleArr[key]);
      if (sum > day) {
        result = false;
        obj[key] = sum;
      }
    }
    return { result, obj };
  }

  // 校验，每个人尽量平均分配工作
  // false { '11': 7, '16': 3 }
  // 员工11 上了7天班，16只上了3天
  checkScheduleAverage(schedule) {
    let result = true;
    let obj = {};
    let arrDay = [];
    let scheduleArr = changeGeshi(schedule);
    for (let key in scheduleArr) {
      let sum = countWordDay(scheduleArr[key]);
      arrDay.push(sum);
    }
    let maxDay = Math.max(...arrDay);
    let minDay = Math.min(...arrDay);
    // 如果最大值班天数、与最小最班天数相差超过2天
    if (maxDay - minDay > 6) {
      let maxE = arrDay.findIndex((item) => item == maxDay);
      let minE = arrDay.findIndex((item) => item == minDay);
      obj[maxE + 1] = maxDay;
      obj[minE + 1] = minDay;
      result = false;
    }
    return { result, obj };
  }
  areAllElementsEqual(array) {
    return array.every((item) => item === Math.max(...array));
  }
  // 校验每个人的班次都有分配到位
  checkScheduleAverage2(schedule) {
    let result = true;
    let obj = {};
    let scheduleArr = changeGeshi(schedule);

    let lArr = [];
    let tmparr = Object.values(scheduleArr);

    console.log(this.extRule.rule2);
    tmparr.map((item, index) => {
      let a = [...new Set(item)];
      lArr.push(a.length);
    });
    // 这几个员工的班次数量校验
    let arr = [];
    this.extRule.rule2.map((item) => {
      arr = arr.concat(item.nurse - 1);
    });
    arr.map((item) => {
      lArr.splice(item, 1);
    });
    result = this.areAllElementsEqual(lArr);
    return { result, obj };
  }
}
