<!-- // 考官管理页面 -->
<template>
  <div class="all">
    <el-table :data="pagination.records" border>
      <el-table-column prop="teacherId" label="ID" width="120"></el-table-column>
      <el-table-column prop="teacherName" label="姓名" width="180"></el-table-column>
      <!-- <el-table-column prop="institute" label="应聘职位" width="200"></el-table-column> -->
      <el-table-column prop="sex" label="性别" width="120"></el-table-column>
      <el-table-column prop="tel" label="联系方式" width="120"></el-table-column>
      <el-table-column prop="email" label="邮箱" width="240"></el-table-column>
      <el-table-column prop="cardId" label="身份证号" width="240"></el-table-column>
      <el-table-column prop="type" label="职称" width="120"></el-table-column>
      <el-table-column fixed="right" label="操作" width="260">
        <template slot-scope="scope">
          <el-button @click="checkGrade(scope.row.teacherId)" type="primary" size="mini"> 编辑</el-button>
          <el-button @click="deleteById(scope.row.teacherId)" type="danger" size="mini"> 删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="pagination.current" :page-sizes="[6, 10]" :page-size="pagination.size"
      layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" class="page">
    </el-pagination>
    <!-- 编辑对话框-->
    <el-dialog title="编辑考官信息" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <section class="update">
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item label="姓名">
            <el-input v-model="form.teacherName"></el-input>
          </el-form-item>
          <!-- <el-form-item label="应聘职位">
            <el-input v-model="form.institute"></el-input>
          </el-form-item> -->
          <el-form-item label="性别">
            <el-input v-model="form.sex"></el-input>
          </el-form-item>
          <el-form-item label="电话号码">
            <el-input v-model="form.tel"></el-input>
          </el-form-item>
          <el-form-item label="密码">
            <el-input v-model="form.pwd"></el-input>
          </el-form-item>
          <el-form-item label="身份证号">
            <el-input v-model="form.cardId"></el-input>
          </el-form-item>
          <el-form-item label="职称">
            <el-input v-model="form.type"></el-input>
          </el-form-item>
        </el-form>
      </section>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" style="font-size: 20px;" size="mini"> 取 消</el-button>
        <el-button type="primary" @click="submit()" size="mini"> 确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getQj , postQj, deleteQj , putQj} from "@/api/index";
export default {
  data() {
    return {
      pagination: {
        //分页后的考试信息
        current: 1, //当前页
        total: null, //记录条数
        size: 6, //每页条数
      },
      dialogVisible: false, //对话框
      form: {}, //保存点击以后当前试卷的信息
    };
  },
  created() {
    this.getTeacherInfo();
  },
  methods: {
    getTeacherInfo() {
      //分页查询所有试卷信息
      getQj(`/teachers/${this.pagination.current}/${this.pagination.size}`).then(res => {
        this.pagination = res.data;
      }).catch(error => { });
    },
    //改变当前记录条数
    handleSizeChange(val) {
      this.pagination.size = val;
      this.getTeacherInfo();
    },
    //改变当前页码，重新发送请求
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.getTeacherInfo();
    },
    checkGrade(teacherId) { //修改教师信息
      this.dialogVisible = true
      getQj(`/teacher/${teacherId}`).then(res => {
        this.form = res.data
      })
    },
    deleteById(teacherId) { //删除当前教师
      this.$confirm("确定删除当前教师吗？删除后无法恢复", "警告", {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'danger'
      }).then(() => { //确认删除
        deleteQj(`/teacher/${teacherId}`).then(res => {
          this.getTeacherInfo()
        })
      }).catch(() => {

      })
    },
    submit() { //提交更改
      // 校验
      if (this.form.teacherName == "") {
        this.$message({
          message: '请输入教师姓名',
          type: 'error'
        })
        return
      }
      // if (this.form.institute == "") {
      //   this.$message({
      //     message: '请输入应聘职位',
      //     type: 'error'
      //   })
      //   return
      // }
      if (this.form.sex == "") {
        this.$message({
          message: '请输入性别',
          type: 'error'
        })
        return
      }
      if (this.form.tel == "") {
        this.$message({
          message: '请输入电话号码',
          type: 'error'
        })
        return
      }
      if(this.form.tel.length > 11) {
        this.$message({
          message: '请输入正确的电话号码',
          type: 'error'
        })
        return
      }
      if (this.form.pwd == "") {
        this.$message({
          message: '请输入密码',
          type: 'error'
        })
        return
      }
      // if (this.form.cardId == "") {
      //   this.$message({
      //     message: '请输入身份证号码',
      //     type: 'error'
      //   })
      //   return
      // }
      // if(this.form.cardId.length > 18) {
      //   this.$message({
      //     message: '请输入正确的身份证号码',
      //     type: 'error'
      //   })
      //   return
      // }
      if (this.form.type == "") {
        this.$message({
          message: '请输入职称',
          type: 'error'
        })
        return
      }
      this.dialogVisible = false
      putQj({
        url: '/teacher',
        method: 'put',
        data: {
          ...this.form
        }
      }).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.$message({
            message: '更新成功',
            type: 'success'
          })
        }
        this.getTeacherInfo()
      })
    },
    handleClose(done) { //关闭提醒
      this.$confirm('确认关闭？')
        .then(_ => {
          done();
        }).catch(_ => { });
    },
  }
};
</script>
<style lang="less" scoped>
.all {
  padding: 0px 40px;

  .page {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .edit {
    margin-left: 20px;
  }

  .el-table tr {
    background-color: #dd5862 !important;
  }
}

.el-table .warning-row {
  background: #000 !important;
}

.el-table .success-row {
  background: #dd5862;
}
</style>
